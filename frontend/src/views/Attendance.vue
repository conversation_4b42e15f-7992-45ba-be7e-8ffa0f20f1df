<template>
  <div class="attendance-page ancient-pixel-container">
    <!-- Main Page Title -->
    <h1>修行日志 · 考勤回顾</h1>

    <!-- 考勤概览统计模块 -->
    <div class="card attendance-summary-module">
      <h2 class="card-title">📊 修行概览</h2>
      <div v-if="loadingStats" class="loading-text">卷宗载入中...</div>
      <div v-else-if="statsError" class="error-text">{{ statsError }}</div>
      <div v-else class="stats-grid">
        <div class="stat-item">
            <span class="stat-label">今日修行时常:</span>
            <span class="stat-value">{{ todayHours }}时</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">本月走火入魔:</span>
            <span class="stat-value">{{ lateCount }}次</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">本月闭关天数:</span>
            <span class="stat-value">{{ leaveCount }}天</span>
        </div>
        <!-- Add more monthly/weekly stats if backend supports -->
      </div>
    </div>

    <!-- 我的打卡日历模块 -->
    <div class="card attendance-calendar-card">
      <h2 class="card-title">📅 我的修行日历</h2>
      <div class="calendar-placeholder">
        <p class="ancient-text">（待集成日历组件）</p>
        <p class="ancient-text">此卷将记载侠士每月修行详情，包括每日入定与出定状态。</p>
      </div>
    </div>

    <!-- 历史打卡记录模块 -->
    <div class="card attendance-records-card">
      <h2 class="card-title">📜 历史修行卷宗</h2>
      <div v-if="loadingStats" class="loading-text">卷宗载入中...</div>
      <div v-else-if="statsError" class="error-text">{{ statsError }}</div>
      <div v-else class="records-list">
        <div v-if="!clockedIn && !clockInTime && !clockOutTime" class="empty-records">
          <p class="ancient-text">暂无今日修行卷宗，速往<router-link to="/dashboard">我的工位</router-link>完成早晚课！</p>
        </div>
        <div v-else class="record-item">
          <div class="record-date ancient-text">今日</div>
          <div class="record-details">
            <p class="ancient-text">入定：{{ clockInTime || '未入定' }}</p>
            <p class="ancient-text">出定：{{ clockOutTime || '未出定' }}</p>
            <p class="ancient-text">修行：{{ todayHours || '0' }}时</p>
            <!-- Here you would iterate through `calendarData` if it contained historical records -->
          </div>
        </div>
        <p class="ancient-text">（更多历史卷宗待后端提供和组件开发）</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useAttendanceStore } from '@/stores/attendance';
import { storeToRefs } from 'pinia';

const attendanceStore = useAttendanceStore();

const {
  clockedIn,
  clockInTime,
  clockOutTime,
  todayHours, // Changed from totalHours
  lateCount,
  leaveCount,
  loadingStats,
  statsError,
  calendarData // Assuming this will eventually hold detailed daily data
} = storeToRefs(attendanceStore);

// No need for handleClockIn or isAnimating as the button is removed
// const handleClockIn = async () => { ... }
// const isAnimating = ref(false);

onMounted(() => {
  attendanceStore.fetchAttendanceData();
});

</script>

<style scoped lang="scss">
// Ensure ancient-pixel.scss variables are available
@use '../styles/ancient-pixel.scss' as ap;

.attendance-page {
  padding: 20px; // Keep base padding
  min-height: calc(100vh - 60px);
  font-family: 'Pixelify Sans', monospace;
  color: var(--color-ancient-dark-brown);
}

h1 {
  color: var(--color-ancient-ink); // Deep ink color
  margin-bottom: 25px; // More space after title
  text-align: left;
  font-size: 2em;
}

.card {
  margin-bottom: 20px; // Space between cards
}

.card-title {
  font-size: 1.5em;
  color: var(--color-ancient-ink);
  margin-bottom: 15px;
  text-align: center;
  border-bottom: 2px dashed var(--color-ancient-light-brown);
  padding-bottom: 10px;
  font-family: 'ZCOOL KuaiLe', serif;
}

.loading-text, .error-text, .empty-records {
  text-align: center;
  color: var(--color-ancient-light-brown);
  font-style: italic;
  padding: 10px 0;
  font-family: 'Noto Serif SC', serif;
}

.error-text {
  color: var(--color-ancient-blood-red);
  font-weight: bold;
}

/* 考勤概览统计模块 */
.attendance-summary-module {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    padding: 10px 0;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--color-ancient-paper);
    border: 2px solid var(--color-ancient-dark-brown);
    border-radius: 0;
    box-shadow: 3px 3px 0px var(--color-ancient-light-brown);
    padding: 15px;
    text-align: center;
    font-family: 'Pixelify Sans', monospace;
  }

  .stat-label {
    font-size: 0.9em;
    color: var(--color-ancient-dark-brown);
    margin-bottom: 5px;
  }

  .stat-value {
    font-size: 1.5em;
    font-weight: bold;
    color: var(--color-ancient-gold); // Highlight stats with gold
    text-shadow: 1px 1px 0px var(--color-ancient-dark-brown);
  }
}

/* 我的打卡日历模块 */
.attendance-calendar-card {
  .calendar-placeholder {
    padding: 20px;
    background-color: var(--color-ancient-highlight);
    border: 2px dashed var(--color-ancient-jade-dark);
    text-align: center;
    border-radius: 0;
    p {
      margin: 5px 0;
    }
  }
}

/* 历史打卡记录模块 */
.attendance-records-card {
  .records-list {
    padding: 10px 0;
  }

  .record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--color-ancient-paper);
    border: 1px solid var(--color-ancient-light-brown);
    border-radius: 0;
    padding: 10px 15px;
    margin-bottom: 8px;
    box-shadow: 1px 1px 0px var(--color-ancient-light-brown);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .record-date {
    font-size: 1.1em;
    font-weight: bold;
    color: var(--color-ancient-ink);
    flex-shrink: 0;
    margin-right: 15px;
  }

  .record-details {
    flex-grow: 1;
    text-align: right;
    p {
      margin: 3px 0;
      font-size: 0.9em;
      color: var(--color-ancient-dark-brown);
      font-family: 'Pixelify Sans', monospace;
    }
  }
  .empty-records {
    a {
      color: var(--color-ancient-jade);
      text-decoration: underline;
      &:hover {
        color: var(--color-ancient-jade-dark);
      }
    }
  }
}


/* 响应式调整 */
@media (max-width: 768px) {
  .attendance-page {
    padding: 15px;
  }

  h1 {
    font-size: 1.8em;
    text-align: center;
  }

  .card-title {
    font-size: 1.3em;
  }

  .attendance-summary-module .stats-grid {
    grid-template-columns: 1fr; /* Stack on smaller screens */
  }

  .attendance-summary-module .stat-item {
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px;
    .stat-value {
      font-size: 1.2em;
    }
  }

  .attendance-calendar-card .calendar-placeholder {
    padding: 15px;
    font-size: 0.9em;
  }
  
  .record-item {
    flex-direction: column;
    align-items: flex-start;
    .record-details {
      width: 100%;
      text-align: left;
    }
  }
}

@media (max-width: 480px) {
   .attendance-page {
      padding: 10px;
   }
    h1 {
      font-size: 1.5em;
      margin-bottom: 15px;
    }

   .card-title {
      font-size: 1.1em;
   }

   .attendance-summary-module .stat-item {
      padding: 8px 12px;
      .stat-label {
        font-size: 0.8em;
      }
      .stat-value {
        font-size: 1em;
      }
   }

   .record-item {
      padding: 8px 10px;
      .record-date {
        font-size: 1em;
      }
      .record-details p {
        font-size: 0.8em;
      }
   }
}
</style>