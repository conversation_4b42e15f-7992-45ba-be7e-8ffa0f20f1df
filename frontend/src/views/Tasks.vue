<template>
  <div class="tasks-page ancient-pixel-container">
    <!-- Main Page Title -->
    <h1>榜文任务 · 江湖历练</h1>

    <!-- Top Filter/Category Tags -->
    <div class="filter-tags">
      <!-- Placeholder for filter buttons -->
      <button
        v-for="tag in filterTags"
        :key="tag.value"
        class="tag-button"
        :class="{ 'active': activeTag === tag.value }"
        @click="setActiveTag(tag.value)"
      >
        {{ tag.label }}
      </button>
    </div>

    <!-- Task List Area -->
    <div class="task-list-container">
      <!-- Placeholder for individual task cards -->
      <div
        v-for="task in filteredTasks"
        :key="task.id"
        class="card task-card"
        :class="{ 'claimed': task.claimed }"
      >
        <div class="task-icon">{{ task.iconPlaceholder }}</div>
        <div class="task-details">
          <div class="task-title">{{ task.title }}</div>
          <div class="task-description">{{ task.description }}</div>
        </div>
        <div class="task-meta">
          <div class="task-reward">💰 +{{ task.reward }} 内力值</div>
          <div class="task-participants">👥 {{ task.participants }}位侠士已参与</div>
          <!-- Conditionally render Claim/View button -->
          <button
            class="claim-button pixel-button primary"
            :class="{ 'claimed': task.claimed }"
            @click="handleTaskAction(task)"
          >
            {{ task.claimed ? '查看任务' : '领取榜文' }}
          </button>
        </div>
      </div>

      <div v-if="filteredTasks.length === 0" class="empty-tasks cartoon-text">
          <!-- Placeholder for cute empty state illustration -->
          <p>当前分类下没有榜文哦，去看看别的吧！</p>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// Placeholder data for filter tags
const filterTags = ref([
  { label: '全部榜文', value: 'all' },
  { label: '门派推荐', value: 'recommended' },
  { label: '我领取的', value: 'my-tasks' },
]);

// Placeholder data for tasks - Added 'claimed' property
const tasks = ref([
  { id: 1, title: '撰写《摸鱼心法》初稿', description: '详细记录今日摸鱼心得和技巧，字数不少于500字。', reward: 50, participants: 15, type: 'general', iconPlaceholder: '✍️', claimed: false },
  { id: 2, title: '设计摸鱼主题表情包', description: '创作一套生动有趣的摸鱼表情，需要包含至少10个表情。', reward: 80, participants: 8, type: 'design', iconPlaceholder: '🎨', claimed: true },
  { id: 3, title: '开发自动喝水提醒工具', description: '一个实用的摸鱼辅助工具，支持定时提醒和自定义语音。', reward: 120, participants: 5, type: 'development', iconPlaceholder: '💻', claimed: false },
  { id: 4, title: '参与摸鱼技巧交流会', description: '分享和学习最新的摸鱼经验，线上线下均可参与。', reward: 30, participants: 30, type: 'general', iconPlaceholder: '💬', claimed: true },
]);

// Reactive state for active tag
const activeTag = ref('all');

// Function to set active tag
const setActiveTag = (tagValue) => {
  activeTag.value = tagValue;
  // Filtering is now handled by the computed property `filteredTasks`
};

// Computed property to filter tasks based on active tag
const filteredTasks = computed(() => {
  switch (activeTag.value) {
    case 'all':
      return tasks.value;
    case 'recommended':
      // 假设官方推荐任务 type 为 'general' 或其他标识
      return tasks.value.filter(task => task.type === 'general'); // 根据 type 过滤官方推荐
    case 'my-tasks':
      return tasks.value.filter(task => task.claimed === true);
    default:
      return tasks.value; // Default to all tasks
  }
});

// Placeholder function for handling task actions (claim/view)
const handleTaskAction = (task) => {
    if (task.claimed) {
        console.log('Viewing task:', task.title);
        // TODO: Implement navigation or modal to view task details
    } else {
        console.log('Claiming task:', task.title);
        // TODO: Implement logic to claim the task (call backend API, update task status)
        // For now, just mark as claimed in frontend data for demo
        task.claimed = true; // 在模拟数据中标记为已领取
    }
};

// TODO: Add logic to fetch real tasks from backend on component mount
</script>

<style scoped lang="scss">
.tasks-page {
  padding: 20px;
  /* Background color inherited from global style */
}

h1 {
  color: #333; /* 深色标题 */
  margin-bottom: 20px;
  text-align: left;
  font-size: 2em; /* 标题大小 */
}

.filter-tags {
  margin-bottom: 20px;
  display: flex;
  gap: 10px; /* Space between tags */
  flex-wrap: wrap; /* Allow tags to wrap on small screens */
}

.tag-button {
  padding: 8px 15px;
  border: 2px solid var(--color-ancient-dark-brown); /* 像素边框 */
  border-radius: 0; /* 锐利边缘 */
  background-color: var(--color-ancient-paper); /* 纸张色背景 */
  color: var(--color-ancient-dark-brown);
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s, box-shadow 0.2s;
  font-family: 'Pixelify Sans', monospace; /* 像素字体 */
  font-size: 0.9em; /* 标签文字大小 */
  box-shadow: 3px 3px 0px var(--color-ancient-light-brown); /* 像素阴影 */

  &:hover {
    background-color: var(--color-ancient-highlight); /* 悬停高亮 */
    transform: translate(-1px, -1px);
    box-shadow: 4px 4px 0px var(--color-ancient-light-brown);
  }
  &:active {
    transform: translate(1px, 1px);
    box-shadow: 2px 2px 0px var(--color-ancient-light-brown);
  }
}

.tag-button.active {
  background-color: #AEE5A9; /* 绿色选中状态背景 */
  color: #333; /* 选中状态文字颜色 */
  font-weight: bold;
  border-color: var(--color-ancient-jade-dark);
  background-color: var(--color-ancient-jade);
  box-shadow: 4px 4px 0px var(--color-ancient-dark-brown); /* 选中状态阴影 */
}

.task-list-container {
  display: grid;
  /* 响应式网格，最小宽度 280px，自动填充 */
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px; /* 卡片之间的间隙 */
}

/*
  The .card class is defined globally (in style.css) and its pixel styles
  should apply automatically.
*/
.task-card {
  display: flex;
  align-items: center; /* Vertically center items in the card */
  gap: 15px; /* Space between sections in the card */
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s, background-color 0.2s;
  position: relative;
  padding: 20px;

  &:hover {
    transform: translate(-3px, -3px); // Pixel "lift"
    box-shadow: 9px 9px 0px var(--color-ancient-light-brown);
    background-color: var(--color-ancient-highlight);
  }

  /* 已领取/完成状态的蒙层 */
  &.claimed::before { /* 使用 ::before 创建蒙层 */
     content: '';
     position: absolute;
     top: 0;
     left: 0;
     right: 0;
     bottom: 0;
     background-color: rgba(220, 220, 220, 0.7); /* Slightly darker semi-transparent gray */
     border-radius: 16px; /* 与卡片相同的圆角 */
     z-index: 1; /* 确保在内容之上 */
  }
  /* 已领取/完成状态下的内容样式 */
  &.claimed {
     cursor: default; /* 改变鼠标样式 */
     .claim-button:not(.claimed) { /* 已领取状态下，未被领取的按钮隐藏或禁用 */
         display: none;
     }
  }
}

.task-icon {
  font-size: 36px; /* Icon size */
  flex-shrink: 0; /* Prevent icon from shrinking */
  z-index: 2; /* 确保在蒙层之上 */
}

// Task card inner text styles
.task-details {
  flex-grow: 1; /* Allow details to take available space */
  display: flex;
  flex-direction: column;
  gap: 5px;
  z-index: 2; /* 确保在蒙层之上 */
  font-family: 'Noto Serif SC', serif;
}

.task-title {
  font-family: 'ZCOOL KuaiLe', serif;
  font-size: 1.1em; /* 标题大小 */
  font-weight: bold;
  color: #333; /* 深色标题 */
}

.task-description {
  font-family: 'Noto Serif SC', serif;
  font-size: 0.9em; /* 简述文字大小 */
  color: var(--color-ancient-dark-brown);
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Limit description to 2 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis; /* 添加省略号 */
  line-height: 1.5;
}

.task-meta {
  display: flex;
  flex-direction: column; /* Meta 信息垂直排列 */
  align-items: flex-end; /* 右侧对齐 */
  gap: 8px; /* Meta 项之间的间隙 */
  flex-shrink: 0; /* Prevent meta area from shrinking */
  z-index: 2; /* 确保在蒙层之上 */
  font-family: 'Pixelify Sans', monospace; // Pixel font for meta data
}

.task-reward,
.task-participants {
   font-size: 0.9em;
   color: var(--color-ancient-dark-brown);
   display: flex; /* 使图标和文字对齐 */
   align-items: center;

   span { /* 图标和数字 */
      margin-right: 5px;
   }
}

.claim-button {
  // Inherits from .pixel-button .primary from ancient-pixel.scss
  // which gives it var(--color-ancient-jade) background and var(--color-ancient-ink) text
  padding: 8px 15px;
  font-size: 0.9em;
  font-weight: 700;
  border-radius: 0; // Sharp corners
  border: 2px solid var(--color-ancient-dark-brown); // Pixel border
  box-shadow: 3px 3px 0px var(--color-ancient-light-brown); // Pixel shadow
  transition: transform 0.2s, box-shadow 0.2s, background-color 0.2s;

  &:hover {
    transform: translate(-1px, -1px);
    box-shadow: 4px 4px 0px var(--color-ancient-light-brown);
  }

  &.claimed { /* 已领取状态按钮样式 (查看任务) */
    background-color: var(--color-ancient-stone-gray); /* Grey background */
    color: var(--color-neutral-white); /* White text */
    border-color: var(--color-ancient-stone-gray-dark);
    cursor: not-allowed; // Should not be clickable if already claimed or just for viewing
    box-shadow: none; // No shadow when claimed
    transform: none; // No transform when claimed
     &:hover {
       background-color: var(--color-ancient-stone-gray-dark); // Darker grey on hover
       opacity: 0.9;
       transform: none; /* No lift */
       box-shadow: none; /* No shadow */
     }
  }
}

.empty-tasks {
  text-align: center;
  color: #777;
  padding: 20px;
  font-family: 'Noto Serif SC', serif;
}

/* **响应式调整** */
@media (max-width: 768px) {
  .tasks-page {
    padding: 15px;
  }

  h1 {
    font-size: 1.8em;
  }

  .filter-tags {
     gap: 8px;
  }

  .tag-button {
     padding: 6px 12px;
     font-size: 0.8em;
  }

  .task-list-container {
     gap: 15px;
     grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* 调整小屏幕最小宽度 */
  }

  .task-card {
     flex-direction: column; /* 在小屏幕上垂直堆叠 */
     align-items: flex-start; /* 左对齐 */
     gap: 10px; /* 调整间隙 */
     padding: 15px;
  }

  .task-icon {
     font-size: 30px;
  }

  .task-details {
     width: 100%; /* 独占一行 */
  }

  .task-title {
     font-size: 1em;
  }

  .task-description {
     font-size: 0.8em;
  }

  .task-meta {
     width: 100%; /* 独占一行 */
     flex-direction: row; /* Meta 信息改为水平排列 */
     justify-content: space-between; /* 左右分散对齐 */
     align-items: center; /* 垂直居中 */
     gap: 10px; /* 调整间隙 */
  }

  .claim-button {
     padding: 6px 12px;
     font-size: 0.8em;
  }

}

@media (max-width: 480px) {
  .tasks-page {
     padding: 10px;
  }
  h1 {
     font-size: 1.5em;
     margin-bottom: 15px;
  }
  .task-list-container {
     gap: 10px;
     grid-template-columns: 1fr; /* 在超小屏幕上单列 */
  }

  .task-card {
     padding: 10px;
  }

   .task-icon {
      font-size: 24px;
   }

  .task-meta {
     flex-direction: column; /* 超小屏幕上 Meta 信息再次垂直排列 */
     align-items: flex-start; /* 左对齐 */
     gap: 5px;
  }
}
</style>