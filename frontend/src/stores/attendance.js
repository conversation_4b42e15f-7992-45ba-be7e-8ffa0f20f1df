import { defineStore } from 'pinia';
import axios from 'axios';
import { useUserStore } from './user'; // 引入 userStore 以获取 token

// 从环境变量获取后端API基地址
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

export const useAttendanceStore = defineStore('attendance', {
  state: () => ({
    // 打卡状态
    clockedIn: false, // 是否已打上班卡 (对应后端 todayStatus.clockedIn)
    clockInTime: '', // 上班打卡时间 (对应后端 todayStatus.clockInTime)
    clockOutTime: '', // 下班打卡时间 (对应后端 todayStatus.clockOutTime)
    isClockingIn: false, // 是否正在执行打卡操作 (用于禁用按钮)

    // 统计数据
    // totalHours: 0, // 仅在后端提供月度总工时时使用
    todayHours: 0, // !! 新增: 对应后端 stats.todayHours
    lateCount: 0, // 本月迟到次数
    leaveCount: 0, // 本月请假天数
    loadingStats: false, // 是否正在加载统计数据
    statsError: null,    // 加载统计数据时的错误

    // 日历数据 (Placeholder for future)
    calendarData: [],
  }),

  getters: {
    // ... (getters 保持不变)
    clockStatusText: (state) => {
       if (!state.clockedIn) return '新的一天，元气满满去"入定"咯！'; // "上班" -> "入定"
       if (state.clockedIn && !state.clockOutTime) return '今日修行未完，切勿懈怠 (^-^)/ '; // "摸鱼" -> "修行未完"
       if (state.clockInTime && state.clockOutTime) return '恭喜出定！今日修行圆满！'; // "辛苦啦" -> "恭喜出定"
       return '';
    }
  },

  actions: {
    // ... (_getCurrentTime 保持不变)
    _getCurrentTime() {
       const now = new Date();
       const hours = String(now.getHours()).padStart(2, '0');
       const minutes = String(now.getMinutes()).padStart(2, '0');
       return `${hours}:${minutes}`;
    },

    async fetchAttendanceData() {
      this.loadingStats = true;
      this.statsError = null;
      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.statsError = '侠士尚未登录，无法查看修行日志。';
        this.loadingStats = false;
        this.clockedIn = false;
        this.clockInTime = '';
        this.clockOutTime = '';
        this.todayHours = 0; // !! 清空
        this.lateCount = 0;
        this.leaveCount = 0;
        return;
      }

      try {
        const response = await axios.get(`${API_BASE_URL}/api/clockin/summary`, {
           headers: {
              Authorization: `Bearer ${token}`
           }
        });

        if (response.data && response.data.success && response.data.data) {
            const { todayStatus, stats } = response.data.data;
            console.log('Fetched attendance summary:', response.data.data);

            this.clockedIn = todayStatus.clockedIn;
            this.clockInTime = todayStatus.clockInTime;
            this.clockOutTime = todayStatus.clockOutTime;

            // !! 修改: 从后端返回的 stats 对象中获取 todayHours
            this.todayHours = stats.todayHours || 0;
            this.lateCount = stats.lateCount || 0;
            this.leaveCount = stats.leaveCount || 0;

        } else {
             this.statsError = response.data.message || '获取修行概览数据失败。';
             console.error('Store: 获取修行概览数据失败:', this.statsError);
             this.clockedIn = false;
             this.clockInTime = '';
             this.clockOutTime = '';
             this.todayHours = 0; // !! 清空
             this.lateCount = 0;
             this.leaveCount = 0;
        }

      } catch (error) {
         console.error('Store: 获取修行概览数据错误:', error);
         this.statsError = error.response?.data?.message || '获取修行概览数据错误，请稍后再试。';
         if (error.response && error.response.status === 401) {
             userStore.setToken(null);
             this.statsError = '身份验证失败，请重新登录。';
         }
         // Make sure to reset state on error too
         this.clockedIn = false;
         this.clockInTime = '';
         this.clockOutTime = '';
         this.todayHours = 0; // !! 清空
         this.lateCount = 0;
         this.leaveCount = 0;
      } finally {
         this.loadingStats = false;
      }
    },

    // ... (performClockIn 保持不变, 它内部会调用 fetchAttendanceData 来刷新状态)
    async performClockIn(type) { // type: 'in' 或 'out'
        this.isClockingIn = true;
        this.statsError = null; 
        const userStore = useUserStore();
        const token = userStore.token;

        if (!token) {
            this.statsError = '侠士尚未登录，无法进行早晚课。';
            this.isClockingIn = false;
            return { success: false, message: this.statsError };
        }

        try {
            const response = await axios.post(`${API_BASE_URL}/api/clockin`, 
              { type: type }, 
              {
                headers: {
                  Authorization: `Bearer ${token}`
                }
              }
            );

            if (response.data && response.data.success) { 
               // 打卡成功后，重新获取考勤概览数据来更新界面
               await this.fetchAttendanceData(); // 等待数据刷新完成

               return { success: true, message: response.data.message || '早课圆满！' }; // More thematic success message

            } else {
               this.statsError = response.data.message || '早晚课失败，请稍后再试。';
               console.error('Store: 早晚课失败:', this.statsError);
               return { success: false, message: this.statsError };
            }
        } catch (error) {
            console.error('Store: 早晚课请求失败:', error);
            this.statsError = error.response?.data?.message || '早晚课请求失败，请检查网络或稍后再试。';
             if (error.response && error.response.status === 401) {
                 userStore.setToken(null);
                 this.statsError = '身份验证失败，请重新登录。';
             }
            return { success: false, message: this.statsError };
        } finally {
            this.isClockingIn = false;
        }
    },
  },
});