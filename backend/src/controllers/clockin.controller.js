// File path: backend/src/controllers/clockin.controller.js
/**
 * 打卡控制器
 * 处理用户打卡相关的业务逻辑
 */

const ClockIn = require('../models/clockin.model');
const User = require('../models/user.model');

/**
 * 格式化日期为YYYY-MM-DD字符串
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
const formatDateString = (date) => {
  const d = date || new Date();
  return d.toISOString().split('T')[0];
};

/**
 * 获取当前时间的HH:MM格式
 * @returns {string} 当前时间，格式为HH:MM
 */
const getCurrentTime = () => {
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
};

/**
 * 计算两个时间字符串之间的小时差
 * @param {string} startTime - 开始时间，格式为HH:MM
 * @param {string} endTime - 结束时间，格式为HH:MM
 * @returns {number} 小时差
 */
const calculateHoursWorked = (startTime, endTime) => {
  const [startHours, startMinutes] = startTime.split(':').map(Number);
  const [endHours, endMinutes] = endTime.split(':').map(Number);
  
  const startDate = new Date();
  startDate.setHours(startHours, startMinutes, 0);
  
  const endDate = new Date();
  endDate.setHours(endHours, endMinutes, 0);
  
  // 如果结束时间小于开始时间，假设是第二天
  if (endDate < startDate) {
    endDate.setDate(endDate.getDate() + 1);
  }
  
  const diffMs = endDate - startDate;
  const diffHours = diffMs / (1000 * 60 * 60);
  
  return parseFloat(diffHours.toFixed(2));
};

/**
 * 打卡（上班/下班）
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.clockIn = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { type, note } = req.body; // type: 'in' 或 'out'
    
    if (!type || !['in', 'out'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: '打卡类型无效，必须是 "in" 或 "out"'
      });
    }
    
    const now = new Date();
    const currentDateString = formatDateString(now);
    const time = getCurrentTime();
    const user = await User.findById(userId);

    if (!user) {
        return res.status(404).json({ success: false, message: '用户不存在' });
    }
    
    // 检查是否有相同类型的打卡记录（同一天内）
    // 对于上班卡，如果当天已打过，则提示。对于下班卡，允许多次打卡，但以最后一次为准（或业务逻辑定义）
    // 为简化，目前逻辑是：同一天内不允许重复打相同类型的卡。
    const existingRecordForTodayType = await ClockIn.findOne({
      user: userId,
      dateString: currentDateString,
      type
    });
    
    if (existingRecordForTodayType) {
      return res.status(400).json({
        success: false,
        message: type === 'in' ? '今天已经打过上班卡了' : '今天已经打过下班卡了'
      });
    }
    
    if (type === 'out') {
      const clockInRecordToday = await ClockIn.findOne({
        user: userId,
        dateString: currentDateString, // 确保是今天的上班卡
        type: 'in'
      }).sort({ date: -1 }); // 获取今天最新的上班卡
      
      if (!clockInRecordToday) {
        return res.status(400).json({
          success: false,
          message: '请先打今天的上班卡'
        });
      }
      
      const hoursWorkedForSession = calculateHoursWorked(clockInRecordToday.time, time);
      
      const clockOutRecord = new ClockIn({
        user: userId,
        date: now,
        dateString: currentDateString,
        type,
        time,
        hoursWorked: hoursWorkedForSession, // 记录本次会话工时
        note
      });
      await clockOutRecord.save();
      
      // 更新用户统计
      if (user.stats.lastDateTodayHoursUpdated === currentDateString) {
        user.stats.todayHours += hoursWorkedForSession;
      } else {
        user.stats.todayHours = hoursWorkedForSession;
      }
      user.stats.lastDateTodayHoursUpdated = currentDateString;
      
      // TODO: 更精确的周工时计算 (例如，如果跨周)
      user.stats.weeklyHours += hoursWorkedForSession;
      user.stats.totalHours += hoursWorkedForSession;
      await user.save();
      
      return res.status(201).json({
        success: true,
        message: '下班打卡成功',
        clockIn: clockOutRecord,
        hoursWorked: user.stats.todayHours // 返回当天的总工时
      });
    }
    
    // type === 'in' (上班打卡)
    if (user.stats.lastDateTodayHoursUpdated && user.stats.lastDateTodayHoursUpdated !== currentDateString) {
      user.stats.todayHours = 0; // 新的一天，重置今日工时
      // 可选：重置周工时
      // const lastDate = new Date(user.stats.lastDateTodayHoursUpdated);
      // const currentDay = now.getDay(); // 0 for Sunday, 1 for Monday...
      // const daysSinceLastUpdate = (now - lastDate) / (1000 * 60 * 60 * 24);
      // if (currentDay < lastDate.getDay() || daysSinceLastUpdate >= 7) { // Basic check for new week
      //   user.stats.weeklyHours = 0;
      // }
    }
    user.stats.lastDateTodayHoursUpdated = currentDateString; // 标记今日工时已针对今天
    await user.save(); // 保存可能的 todayHours 和 lastDateTodayHoursUpdated 重置

    const newClockInRecord = new ClockIn({
      user: userId,
      date: now,
      dateString: currentDateString,
      type,
      time,
      note
    });
    
    await newClockInRecord.save();
    
    res.status(201).json({
      success: true,
      message: '上班打卡成功',
      clockIn: newClockInRecord
    });

  } catch (error) {
    next(error);
  }
};

// ... getClockIns, getTodayClockIns remains the same ...
exports.getClockIns = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate, type } = req.query;
    
    // 构建查询条件
    const query = { user: userId };
    
    // 如果提供了开始日期和结束日期，按日期范围查询
    if (startDate && endDate) {
      query.dateString = { $gte: startDate, $lte: endDate };
    } else if (startDate) {
      query.dateString = { $gte: startDate };
    } else if (endDate) {
      query.dateString = { $lte: endDate };
    }
    
    // 如果提供了打卡类型，按类型查询
    if (type && ['in', 'out'].includes(type)) {
      query.type = type;
    }
    
    const clockIns = await ClockIn.find(query)
      .sort({ date: -1 })
      .limit(parseInt(req.query.limit) || 30);
    
    res.json({
      success: true,
      clockIns
    });
  } catch (error) {
    next(error);
  }
};

exports.getTodayClockIns = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const today = formatDateString(new Date());
    
    const clockIns = await ClockIn.find({
      user: userId,
      dateString: today
    }).sort({ date: 1 });
    
    res.json({
      success: true,
      clockIns,
      today
    });
  } catch (error) {
    next(error);
  }
};


/**
 * 获取用户考勤概览：今日打卡状态和统计数据
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.getAttendanceSummary = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const todayDateString = formatDateString(new Date());

    const user = await User.findById(userId).select('stats');
    if (!user) {
        return res.status(404).json({ success: false, message: '用户不存在' });
    }

    let statsForFrontend = { ...user.stats }; // Make a copy to modify for response

    // 1. 处理 todayHours 和打卡状态的逻辑
    let todayStatus = {
      clockedIn: false,
      clockInTime: '',
      clockOutTime: '',
    };

    if (user.stats.lastDateTodayHoursUpdated && user.stats.lastDateTodayHoursUpdated !== todayDateString) {
      // Stored todayHours is for a previous day. For today's summary, it's 0.
      statsForFrontend.todayHours = 0;
      // todayStatus remains as fresh (not clocked in for today)
    } else {
      // lastDateTodayHoursUpdated IS todayDateString OR it's empty (new user, first summary)
      // Fetch today's actual clock-in records to determine status
      const todayRecords = await ClockIn.find({
        user: userId,
        dateString: todayDateString
      }).sort({ date: 1 });

      let firstIn = null;
      let lastOut = null;

      todayRecords.forEach(record => {
          if (record.type === 'in') {
              if (!firstIn || record.date > firstIn.date) firstIn = record;
          }
          if (record.type === 'out') {
              if (!lastOut || record.date > lastOut.date) lastOut = record;
          }
      });

      if (firstIn) {
          todayStatus.clockedIn = true; // User is considered clocked in if there's an 'in' record for today
          todayStatus.clockInTime = firstIn.time;
      }
      if (lastOut) {
          todayStatus.clockOutTime = lastOut.time;
          // If there's a clock-out, clockedIn status might depend on whether it's the *latest* action.
          // For simplicity, if there's an 'in' and an 'out', we show both. Frontend handles "已打卡完毕".
          // If only 'out' exists without 'in' (should be rare), clockedIn remains false.
      }
      
      // If only an 'in' record, and no 'out' record yet for today,
      // user.stats.todayHours might be 0 (if first 'in' of the day) or cumulative from earlier sessions today.
      // The frontend will calculate ongoing time if clockInTime is set and clockOutTime is not.
      // If there's a clock-out, user.stats.todayHours should reflect the total for the day.
      statsForFrontend.todayHours = user.stats.todayHours;
    }

    // Remove sensitive or internal fields from stats before sending
    delete statsForFrontend.lastDateTodayHoursUpdated;


    // 2. 模拟/准备其他统计数据 (如迟到、请假等)
    // These would typically be calculated based on historical data or other models
    statsForFrontend.lateCount = statsForFrontend.lateCount || 0; // Placeholder
    statsForFrontend.leaveCount = statsForFrontend.leaveCount || 0; // Placeholder


    res.json({
      success: true,
      message: '获取考勤概览成功',
      data: {
        todayStatus,
        stats: statsForFrontend
      }
    });

  } catch (error) {
    next(error);
  }
};