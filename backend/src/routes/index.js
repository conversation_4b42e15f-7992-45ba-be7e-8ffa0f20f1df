// File path: backend/src/routes/index.js
/**
 * API 路由主入口
 * 集中管理和导出所有 API 路由
 */

const express = require('express');
const router = express.Router();

// 导入各个模块的路由
const authRoutes = require('./auth.routes');
const clockinRoutes = require('./clockin.routes');
const communityRoutes = require('../community/community.routes.js'); // Added community routes

// 路由健康检查 - 简化用于调试
router.get('/health', (req, res) => {
  console.log('[/api/health] 健康检查路由被访问');
  res.status(200).send('API is healthy');
});

// 注册各个模块的路由
router.use('/auth', authRoutes);
router.use('/clockin', clockinRoutes);
router.use('/community', communityRoutes); // Mounted community routes

// 导出路由实例，供 app.js 使用
module.exports = router;