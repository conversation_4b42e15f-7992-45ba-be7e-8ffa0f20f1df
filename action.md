好的，我们现在开始“阶段一：核心体验强化与沉浸式基础”的第二个主要部分：“**修行日志 (Attendance) - 完善核心打卡闭环**”。

这个迭代将专注于：
*   在后端实现“迟到/早退”的判断逻辑，并更新打卡记录的状态和用户的月度迟到计数。
*   在前端“修行日志”页面展示一个简单、美观的日历，能够切换月份并高亮有记录的日期。
*   日历中的日期会显示迟到/早退的指示，点击日期能查看当日的详细打卡记录。
*   “历史修行卷宗”部分会展示所有已获取的打卡记录，包含其状态。

---

### 阶段一：核心体验强化与沉浸式基础

#### 2. 修行日志 (Attendance) - 完善核心打卡闭环

**改动点概述：**

1.  **后端 `backend/src/models/user.model.js`：** 添加 `monthlyLateCount` 和 `lastMonthLateCountUpdated` 字段到用户统计数据中。
2.  **后端 `backend/src/controllers/clockin.controller.js`：**
    *   新增辅助函数 `formatTimeToMinutes`。
    *   修改 `clockIn` 函数以根据预设的作息时间判断打卡状态 (`normal`, `late`, `early`)，并更新打卡记录的 `status` 字段。
    *   在 `clockIn` 函数中，如果用户迟到，更新 `User.stats.monthlyLateCount`，并在新月份时重置该计数。
    *   修改 `getClockIns` 接口，使其支持按 `year` 和 `month` 查询。
    *   修改 `getAttendanceSummary` 接口，返回 `monthlyLateCount`。
3.  **前端 `frontend/src/stores/attendance.js`：**
    *   更新 `fetchAttendanceData` 以使用后端返回的 `monthlyLateCount`。
    *   新增 `monthlyRecords` 状态，用于存储按月获取的打卡记录。
    *   新增 `fetchMonthlyRecords` action，调用 `GET /api/clockin` 接口按年月获取数据。
    *   新增 `getRecordStatusDisplay` getter，用于根据打卡状态返回对应的文本和颜色。
4.  **前端 `frontend/src/views/Attendance.vue`：**
    *   使用 `attendanceStore` 中的 `monthlyRecords` 状态来渲染日历和历史记录。
    *   实现日历组件：显示当前月份、切换月份按钮、日历网格、高亮今天/有记录的日期。
    *   在日历日期上显示迟到/早退的简短指示。
    *   点击日历日期时，显示该日期的详细打卡记录。
    *   更新“历史修行卷宗”部分，展示从后端获取的包含状态的打卡记录。

---

**详细代码修改：**

### **1. 后端修改**

**1.1. `backend/src/models/user.model.js`**

为用户统计数据添加月度迟到计数和更新时间标记。

```javascript
// File path: backend/src/models/user.model.js
/**
 * 用户模型
 * 定义用户数据结构，用于与数据库交互
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const Schema = mongoose.Schema;

/**
 * 用户 Schema
 * 定义用户数据在 MongoDB 中的结构
 */
const userSchema = new Schema(
  {
    // ... 其他字段保持不变
    username: {
      type: String,
      required: [true, '用户名不能为空'],
      trim: true,
      maxlength: [50, '用户名不能超过50个字符']
    },
    email: {
      type: String,
      required: [true, '邮箱不能为空'],
      unique: true,
      trim: true,
      lowercase: true,
      match: [/^\S+@\S+\.\S+$/, '请提供有效的邮箱地址']
    },
    password: {
      type: String,
      required: [true, '密码不能为空'],
      minlength: [6, '密码至少需要6个字符']
    },
    role: {
      type: String,
      enum: ['user', 'admin'],
      default: 'user'
    },
    avatar: {
      type: String,
      default: '/uploads/avatars/demo.jpeg' // 确保默认头像路径正确
    },
    nickname: {
      type: String,
      default: function() {
        return this.username;
      }
    },
    position: {
      type: String,
      default: '初级摸鱼师'
    },
    signature: {
      type: String,
      default: '此心安处是吾乡',
      maxlength: [100, '个性签名不能超过100个字符']
    },
    stats: {
      totalHours: { type: Number, default: 0 }, // 总工时
      todayHours: { type: Number, default: 0 }, // 今日工时
      weeklyHours: { type: Number, default: 0 }, // 本周工时 (简化为累加，复杂重置逻辑可后续)
      points: { type: Number, default: 100 }, // 积分
      consecutiveClockIns: { type: Number, default: 0 }, // 连续打卡天数
      totalClockIns: { type: Number, default: 0 }, // 总打卡天数

      lastDateTodayHoursUpdated: { type: String, default: '' }, // YYYY-MM-DD
      monthlyLateCount: { type: Number, default: 0 }, // **新增：本月迟到次数**
      lastMonthLateCountUpdated: { type: String, default: '' } // **新增：YYYY-MM 用于重置月度迟到计数**
    },
    preferences: {
      theme: { type: String, default: 'default' },
      notifications: { type: Boolean, default: true }
    },
    isActive: {
      type: Boolean,
      default: true
    },
    lastLogin: {
      type: Date,
      default: null
    }
  },
  {
    timestamps: true
  }
);

// ... 索引、密码加密中间件、方法保持不变

const User = mongoose.model('User', userSchema);

module.exports = User;
```

**1.2. `backend/src/controllers/clockin.controller.js`**

引入新的辅助函数，修改 `clockIn` 和 `getClockIns`、`getAttendanceSummary`。

```javascript
// File path: backend/src/controllers/clockin.controller.js
/**
 * 打卡控制器
 * 处理用户打卡相关的业务逻辑
 */

const ClockIn = require('../models/clockin.model');
const User = require('../models/user.model');

// **新增：标准作息时间（硬编码，未来可配置或从用户设置中读取）**
const STANDARD_WORK_START = '09:00';
const STANDARD_WORK_END = '18:00';
const GRACE_PERIOD_MINUTES = 10; // 迟到/早退的宽限时间（分钟）

/**
 * 格式化日期为YYYY-MM-DD字符串
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
const formatDateString = (date) => {
  const d = date || new Date();
  return d.toISOString().split('T')[0];
};

/**
 * 获取当前时间的HH:MM格式
 * @returns {string} 当前时间，格式为HH:MM
 */
const getCurrentTime = () => {
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
};

/**
 * 将 HH:MM 时间字符串转换为分钟数
 * @param {string} timeString - HH:MM 格式的时间字符串
 * @returns {number} 从午夜开始的分钟数
 */
const formatTimeToMinutes = (timeString) => {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
};

/**
 * 计算两个时间字符串之间的小时差
 * @param {string} startTime - 开始时间，格式为HH:MM
 * @param {string} endTime - 结束时间，格式为HH:MM
 * @returns {number} 小时差
 */
const calculateHoursWorked = (startTime, endTime) => {
  const [startHours, startMinutes] = startTime.split(':').map(Number);
  const [endHours, endMinutes] = endTime.split(':').map(Number);

  const startDate = new Date();
  startDate.setHours(startHours, startMinutes, 0);

  const endDate = new Date();
  endDate.setHours(endHours, endMinutes, 0);

  // 如果结束时间小于开始时间，假设是第二天 (处理跨夜班场景)
  if (endDate < startDate) {
    endDate.setDate(endDate.getDate() + 1);
  }

  const diffMs = endDate - startDate;
  const diffHours = diffMs / (1000 * 60 * 60);

  return parseFloat(diffHours.toFixed(2));
};

/**
 * 打卡（上班/下班）
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.clockIn = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { type, note } = req.body; // type: 'in' 或 'out'

    if (!type || !['in', 'out'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: '打卡类型无效，必须是 "in" 或 "out"'
      });
    }

    const now = new Date();
    const currentDateString = formatDateString(now);
    const currentTime = getCurrentTime(); // 获取当前时间 HH:MM
    const currentUserTimeInMinutes = formatTimeToMinutes(currentTime);

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ success: false, message: '用户不存在' });
    }

    // 检查是否有相同类型的打卡记录（同一天内）
    const existingRecordForTodayType = await ClockIn.findOne({
      user: userId,
      dateString: currentDateString,
      type
    });

    if (existingRecordForTodayType) {
      return res.status(400).json({
        success: false,
        message: type === 'in' ? '今天已经打过上班卡了' : '今天已经打过下班卡了'
      });
    }

    // 更新用户今日工时统计的日期标记
    if (user.stats.lastDateTodayHoursUpdated && user.stats.lastDateTodayHoursUpdated !== currentDateString) {
      user.stats.todayHours = 0; // 新的一天，重置今日工时
    }
    user.stats.lastDateTodayHoursUpdated = currentDateString; // 标记今日工时已针对今天

    // **处理迟到/早退逻辑和更新用户统计**
    let clockInStatus = 'normal'; // 默认状态
    const currentMonthString = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;

    if (type === 'in') {
      const standardStartMinutes = formatTimeToMinutes(STANDARD_WORK_START);
      if (currentUserTimeInMinutes > standardStartMinutes + GRACE_PERIOD_MINUTES) {
        clockInStatus = 'late';
        // 更新月度迟到计数
        if (user.stats.lastMonthLateCountUpdated !== currentMonthString) {
          user.stats.monthlyLateCount = 0; // 新的月份，重置月度迟到计数
          user.stats.lastMonthLateCountUpdated = currentMonthString;
        }
        user.stats.monthlyLateCount += 1;
      }
    } else if (type === 'out') {
      const standardEndMinutes = formatTimeToMinutes(STANDARD_WORK_END);
      if (currentUserTimeInMinutes < standardEndMinutes - GRACE_PERIOD_MINUTES) {
        clockInStatus = 'early';
      }

      const clockInRecordToday = await ClockIn.findOne({
        user: userId,
        dateString: currentDateString,
        type: 'in'
      }).sort({ date: -1 }); // 获取今天最新的上班卡

      if (!clockInRecordToday) {
        // 如果没有找到上班卡，下班卡逻辑不执行，但在实际中前端应该防止这种情况
        return res.status(400).json({
          success: false,
          message: '请先打今天的上班卡'
        });
      }

      const hoursWorkedForSession = calculateHoursWorked(clockInRecordToday.time, currentTime);

      const clockOutRecord = new ClockIn({
        user: userId,
        date: now,
        dateString: currentDateString,
        type,
        time: currentTime, // 使用当前时间
        hoursWorked: hoursWorkedForSession, // 记录本次会话工时
        note,
        status: clockInStatus // 应用计算出的状态
      });
      await clockOutRecord.save();

      user.stats.todayHours += hoursWorkedForSession;
      user.stats.weeklyHours += hoursWorkedForSession; // 简单累加，复杂周计算后续再考虑
      user.stats.totalHours += hoursWorkedForSession;
      await user.save(); // 保存用户统计更新

      return res.status(201).json({
        success: true,
        message: '下班打卡成功',
        clockIn: clockOutRecord,
        hoursWorked: user.stats.todayHours, // 返回当天的总工时
        monthlyLateCount: user.stats.monthlyLateCount // 返回本月迟到计数
      });
    }

    // type === 'in' (上班打卡)
    await user.save(); // 保存可能的 todayHours 重置和月度迟到统计更新

    const newClockInRecord = new ClockIn({
      user: userId,
      date: now,
      dateString: currentDateString,
      type,
      time: currentTime, // 使用当前时间
      note,
      status: clockInStatus // 应用计算出的状态
    });

    await newClockInRecord.save();

    res.status(201).json({
      success: true,
      message: '上班打卡成功',
      clockIn: newClockInRecord,
      monthlyLateCount: user.stats.monthlyLateCount // 返回本月迟到计数
    });

  } catch (error) {
    console.error('打卡控制器错误:', error);
    next(error);
  }
};

/**
 * 获取用户打卡记录
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.getClockIns = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate, type, year, month } = req.query; // **修改：新增 year 和 month 筛选**

    // 构建查询条件
    const query = { user: userId };

    // 如果提供了年和月，则优先按月查询
    if (year && month) {
        // 构建该年月的开始和结束日期字符串
        const startOfMonth = new Date(Date.UTC(parseInt(year), parseInt(month) - 1, 1));
        const endOfMonth = new Date(Date.UTC(parseInt(year), parseInt(month), 0, 23, 59, 59, 999)); // 月份参数为0表示上个月的最后一天

        const startOfMonthString = formatDateString(startOfMonth);
        const endOfMonthString = formatDateString(endOfMonth);

        query.dateString = { $gte: startOfMonthString, $lte: endOfMonthString };
    }
    // 如果提供了开始日期和结束日期，按日期范围查询 (兼容旧的，优先级低于年/月)
    else if (startDate && endDate) {
      query.dateString = { $gte: startDate, $lte: endDate };
    } else if (startDate) {
      query.dateString = { $gte: startDate };
    } else if (endDate) {
      query.dateString = { $lte: endDate };
    }

    // 如果提供了打卡类型，按类型查询
    if (type && ['in', 'out'].includes(type)) {
      query.type = type;
    }

    const clockIns = await ClockIn.find(query)
      .sort({ date: -1 }) // 按日期倒序
      .limit(parseInt(req.query.limit) || 100); // 增加默认限制，或移除限制以获取全月数据

    res.json({
      success: true,
      clockIns
    });
  } catch (error) {
    next(error);
  }
};

exports.getTodayClockIns = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const today = formatDateString(new Date());

    const clockIns = await ClockIn.find({
      user: userId,
      dateString: today
    }).sort({ date: 1 });

    res.json({
      success: true,
      clockIns,
      today
    });
  } catch (error) {
    next(error);
  }
};


/**
 * 获取用户考勤概览：今日打卡状态和统计数据
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.getAttendanceSummary = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const todayDateString = formatDateString(new Date());
    const currentMonthString = `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, '0')}`;

    const user = await User.findById(userId).select('stats');
    if (!user) {
        return res.status(404).json({ success: false, message: '用户不存在' });
    }

    let statsForFrontend = { ...user.stats }; // Make a copy to modify for response

    // 1. 处理 todayHours 和打卡状态的逻辑
    let todayStatus = {
      clockedIn: false,
      clockInTime: '',
      clockOutTime: '',
    };

    // 如果上次更新的日期不是今天，重置今日工时
    if (user.stats.lastDateTodayHoursUpdated && user.stats.lastDateTodayHoursUpdated !== todayDateString) {
      statsForFrontend.todayHours = 0;
    }
    // 如果月度迟到计数不是当前月份的，重置月度迟到计数
    if (user.stats.lastMonthLateCountUpdated !== currentMonthString) {
      statsForFrontend.monthlyLateCount = 0;
    }


    // 获取今日的打卡记录来判断当前状态
    const todayRecords = await ClockIn.find({
      user: userId,
      dateString: todayDateString
    }).sort({ date: 1 }); // 升序获取，in在前，out在后

    let firstIn = null;
    let lastOut = null;

    todayRecords.forEach(record => {
        if (record.type === 'in') {
            // 找到当天最早的上班卡
            if (!firstIn || record.date < firstIn.date) firstIn = record;
        }
        if (record.type === 'out') {
            // 找到当天最晚的下班卡
            if (!lastOut || record.date > lastOut.date) lastOut = record;
        }
    });

    if (firstIn) {
        todayStatus.clockedIn = true;
        todayStatus.clockInTime = firstIn.time;
    }
    if (lastOut) {
        todayStatus.clockOutTime = lastOut.time;
    }

    // 2. 模拟/准备其他统计数据 (如请假等)
    statsForFrontend.leaveCount = statsForFrontend.leaveCount || 0; // Placeholder

    res.json({
      success: true,
      message: '获取考勤概览成功',
      data: {
        todayStatus,
        stats: { // 明确要返回的统计数据
          todayHours: statsForFrontend.todayHours,
          monthlyLateCount: statsForFrontend.monthlyLateCount, // **返回月度迟到计数**
          leaveCount: statsForFrontend.leaveCount,
          totalHours: statsForFrontend.totalHours, // 返回总工时
          weeklyHours: statsForFrontend.weeklyHours, // 返回周工时
        }
      }
    });

  } catch (error) {
    console.error('获取考勤概览控制器错误:', error);
    next(error);
  }
};
```

### **2. 前端修改**

**2.1. `frontend/src/stores/attendance.js`**

更新 Pinia store，处理新的 `monthlyLateCount` 和月度记录获取逻辑。

```javascript
// File path: frontend/src/stores/attendance.js
import { defineStore } from 'pinia';
import axios from 'axios';
import { useUserStore } from './user';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

export const useAttendanceStore = defineStore('attendance', {
  state: () => ({
    clockedIn: false,
    clockInTime: '',
    clockOutTime: '',
    isClockingIn: false,

    todayHours: 0,
    lateCount: 0, // **现在表示本月迟到次数 (monthlyLateCount)**
    leaveCount: 0, // 暂为占位符

    loadingStats: false,
    statsError: null,

    // **新增：月度打卡记录**
    monthlyRecords: [], // 用于存储某个月的所有打卡记录
    loadingMonthlyRecords: false,
    monthlyRecordsError: null,
  }),

  getters: {
    clockStatusText: (state) => {
       if (!state.clockedIn) return '新的一天，元气满满去"入定"咯！';
       if (state.clockedIn && !state.clockOutTime) return '今日修行未完，切勿懈怠 (^-^)/ ';
       if (state.clockInTime && state.clockOutTime) return '恭喜出定！今日修行圆满！';
       return '';
    },
    // **新增：根据状态获取对应颜色和文本**
    getRecordStatusDisplay: () => (status) => {
      switch (status) {
        case 'late':
          return { text: '走火', color: 'var(--color-ancient-blood-red)' }; // 红色
        case 'early':
          return { text: '早退', color: 'orange' }; // 橙色
        case 'makeup':
          return { text: '补签', color: 'var(--color-ancient-jade)' }; // 绿色
        default:
          return { text: '正常', color: 'var(--color-ancient-dark-brown)' }; // 正常色
      }
    }
  },

  actions: {
    _getCurrentTime() {
       const now = new Date();
       const hours = String(now.getHours()).padStart(2, '0');
       const minutes = String(now.getMinutes()).padStart(2, '0');
       return `${hours}:${minutes}`;
    },

    async fetchAttendanceData() {
      this.loadingStats = true;
      this.statsError = null;
      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.statsError = '侠士尚未登录，无法查看修行日志。';
        this.loadingStats = false;
        this.resetAttendanceState();
        return;
      }

      try {
        const response = await axios.get(`${API_BASE_URL}/api/clockin/summary`, {
           headers: {
              Authorization: `Bearer ${token}`
           }
        });

        if (response.data && response.data.success && response.data.data) {
            const { todayStatus, stats } = response.data.data;
            console.log('Fetched attendance summary:', response.data.data);

            this.clockedIn = todayStatus.clockedIn;
            this.clockInTime = todayStatus.clockInTime;
            this.clockOutTime = todayStatus.clockOutTime;

            this.todayHours = stats.todayHours || 0;
            this.lateCount = stats.monthlyLateCount || 0; // **修改：从 monthlyLateCount 获取**
            this.leaveCount = stats.leaveCount || 0;

        } else {
             this.statsError = response.data.message || '获取修行概览数据失败。';
             console.error('Store: 获取修行概览数据失败:', this.statsError);
             this.resetAttendanceState();
        }

      } catch (error) {
         console.error('Store: 获取修行概览数据错误:', error);
         this.statsError = error.response?.data?.message || '获取修行概览数据错误，请稍后再试。';
         if (error.response && error.response.status === 401) {
             userStore.setToken(null);
             this.statsError = '身份验证失败，请重新登录。';
         }
         this.resetAttendanceState();
      } finally {
         this.loadingStats = false;
      }
    },

    async performClockIn(type) {
        this.isClockingIn = true;
        this.statsError = null;
        const userStore = useUserStore();
        const token = userStore.token;

        if (!token) {
            this.statsError = '侠士尚未登录，无法进行早晚课。';
            this.isClockingIn = false;
            return { success: false, message: this.statsError };
        }

        try {
            const response = await axios.post(`${API_BASE_URL}/api/clockin`,
              { type: type },
              {
                headers: {
                  Authorization: `Bearer ${token}`
                }
              }
            );

            if (response.data && response.data.success) {
               await this.fetchAttendanceData(); // 等待数据刷新完成
               // 打卡后，刷新当前月的记录，以便日历和历史记录更新
               const now = new Date();
               await this.fetchMonthlyRecords(now.getFullYear(), now.getMonth() + 1);
               return { success: true, message: response.data.message || '早课圆满！' };

            } else {
               this.statsError = response.data.message || '早晚课失败，请稍后再试。';
               console.error('Store: 早晚课失败:', this.statsError);
               return { success: false, message: this.statsError };
            }
        } catch (error) {
            console.error('Store: 早晚课请求失败:', error);
            this.statsError = error.response?.data?.message || '早晚课请求失败，请检查网络或稍后再试。';
             if (error.response && error.response.status === 401) {
                 userStore.setToken(null);
                 this.statsError = '身份验证失败，请重新登录。';
             }
            return { success: false, message: this.statsError };
        } finally {
            this.isClockingIn = false;
        }
    },

    // **新增：获取月度打卡记录**
    async fetchMonthlyRecords(year, month) {
      this.loadingMonthlyRecords = true;
      this.monthlyRecordsError = null;
      this.monthlyRecords = [];

      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.monthlyRecordsError = '侠士尚未登录，无法查看历史卷宗。';
        this.loadingMonthlyRecords = false;
        return;
      }

      try {
        const response = await axios.get(`${API_BASE_URL}/api/clockin`, {
          params: { year, month }, // 传递年月参数
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (response.data && response.data.success) {
          this.monthlyRecords = response.data.clockIns;
          console.log(`Store: 获取到 ${year}年${month}月 打卡记录:`, this.monthlyRecords.length, '条');
        } else {
          this.monthlyRecordsError = response.data.message || '获取月度打卡记录失败。';
          console.error('Store: 获取月度打卡记录失败:', this.monthlyRecordsError);
        }
      } catch (error) {
        console.error('Store: 获取月度打卡记录请求错误:', error);
        this.monthlyRecordsError = error.response?.data?.message || '获取月度打卡记录错误，请稍后再试。';
        if (error.response && error.response.status === 401) {
             userStore.setToken(null);
        }
      } finally {
        this.loadingMonthlyRecords = false;
      }
    },

    resetAttendanceState() {
      this.clockedIn = false;
      this.clockInTime = '';
      this.clockOutTime = '';
      this.todayHours = 0;
      this.lateCount = 0;
      this.leaveCount = 0;
      this.monthlyRecords = [];
    }
  },
});
```

**2.2. `frontend/src/views/Attendance.vue`**

改造日历和历史记录展示，引入新的计算属性和方法来支持日历交互。

```vue
<template>
  <div class="attendance-page ancient-pixel-container">
    <!-- Main Page Title -->
    <h1>修行日志 · 考勤回顾</h1>

    <!-- 考勤概览统计模块 -->
    <div class="card attendance-summary-module">
      <h2 class="card-title">📊 修行概览</h2>
      <div v-if="loadingStats" class="loading-text">卷宗载入中...</div>
      <div v-else-if="statsError" class="error-text">{{ statsError }}</div>
      <div v-else class="stats-grid">
        <div class="stat-item">
            <span class="stat-label">今日修行时常:</span>
            <span class="stat-value">{{ todayHours }}时</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">本月走火入魔:</span>
            <span class="stat-value">{{ lateCount }}次</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">本月闭关天数:</span>
            <span class="stat-value">{{ leaveCount }}天</span>
        </div>
      </div>
    </div>

    <!-- 我的打卡日历模块 -->
    <div class="card attendance-calendar-card">
      <h2 class="card-title">📅 我的修行日历</h2>
      <div v-if="loadingMonthlyRecords" class="loading-text">日历卷宗载入中...</div>
      <div v-else-if="monthlyRecordsError" class="error-text">{{ monthlyRecordsError }}</div>
      <div v-else class="calendar-content">
        <div class="calendar-header">
          <button @click="changeMonth(-1)" class="pixel-button">&lt; 前一月</button>
          <span class="current-month-year">{{ currentYear }}年 {{ currentMonth + 1 }}月</span>
          <button @click="changeMonth(1)" class="pixel-button">后一月 &gt;</button>
        </div>
        <div class="weekdays">
          <span v-for="day in weekdays" :key="day">{{ day }}</span>
        </div>
        <div class="calendar-grid">
          <div
            v-for="(day, index) in calendarDays"
            :key="index"
            class="calendar-day"
            :class="{
              'is-today': day.isToday,
              'has-records': day.hasRecords,
              'is-current-month': day.isCurrentMonth,
              'is-selected': selectedDate && day.date && day.date.toDateString() === selectedDate.toDateString()
            }"
            @click="selectDay(day)"
          >
            <span v-if="day.dayOfMonth !== null" class="day-number">{{ day.dayOfMonth }}</span>
            <div v-if="day.hasRecords" class="record-indicator">
              <!-- 显示迟到或早退的简短指示 -->
              <span v-if="day.status.includes('late')" class="status-indicator late" title="迟到">走</span>
              <span v-if="day.status.includes('early')" class="status-indicator early" title="早退">早</span>
            </div>
          </div>
        </div>
        <!-- 日期详情显示 -->
        <div v-if="selectedDateDetails" class="selected-date-details ancient-pixel-container detail-card">
            <h3 class="card-title">
              {{ selectedDateDetails.dateString }} 修行详情
            </h3>
            <div v-if="selectedDateDetails.records.length > 0">
                <div v-for="record in selectedDateDetails.records" :key="record._id" class="detail-record-item">
                    <span class="record-type" :style="{ color: getRecordStatusDisplay(record.type === 'in' ? record.status : '').color }">
                        {{ record.type === 'in' ? '入定' : '出定' }}
                    </span>
                    <span class="record-time">{{ record.time }}</span>
                    <span v-if="record.type === 'out' && record.hoursWorked > 0" class="record-hours">(修行: {{ record.hoursWorked }}时)</span>
                    <span class="record-status" :style="{ color: getRecordStatusDisplay(record.status).color }">
                        [{{ getRecordStatusDisplay(record.status).text }}]
                    </span>
                </div>
            </div>
            <p v-else class="empty-text">今日无修行记录。</p>
        </div>
      </div>
    </div>

    <!-- 历史打卡记录模块 -->
    <div class="card attendance-records-card">
      <h2 class="card-title">📜 历史修行卷宗</h2>
      <div v-if="loadingMonthlyRecords" class="loading-text">卷宗载入中...</div>
      <div v-else-if="monthlyRecordsError" class="error-text">{{ monthlyRecordsError }}</div>
      <div v-else class="records-list">
        <div v-if="allClockIns.length === 0" class="empty-records">
          <p class="ancient-text">此卷宗暂无记录。</p>
        </div>
        <div v-else class="record-item" v-for="record in allClockIns" :key="record._id">
          <div class="record-date ancient-text">{{ record.dateString }}</div>
          <div class="record-details">
            <p class="ancient-text">
                {{ record.type === 'in' ? '入定' : '出定' }}：{{ record.time }}
                <span v-if="record.type === 'out' && record.hoursWorked > 0" class="record-hours-worked">(修行: {{ record.hoursWorked }}时)</span>
                <span class="record-status" :style="{ color: getRecordStatusDisplay(record.status).color }">
                  [{{ getRecordStatusDisplay(record.status).text }}]
                </span>
            </p>
            <p v-if="record.note" class="record-note">备注：{{ record.note }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useAttendanceStore } from '@/stores/attendance';
import { storeToRefs } from 'pinia';

const attendanceStore = useAttendanceStore();

const {
  clockedIn,
  clockInTime,
  clockOutTime,
  todayHours,
  lateCount, // Now represents monthlyLateCount
  leaveCount,
  loadingStats,
  statsError,
  monthlyRecords, // New: monthly records for calendar
  loadingMonthlyRecords,
  monthlyRecordsError,
  getRecordStatusDisplay // New getter for status display
} = storeToRefs(attendanceStore);

// Calendar State
const currentMonth = ref(new Date().getMonth());
const currentYear = ref(new Date().getFullYear());
const selectedDate = ref(null); // The date selected in the calendar

const weekdays = ['日', '一', '二', '三', '四', '五', '六'];

// Helper to format date to YYYY-MM-DD
const formatDateToYYYYMMDD = (date) => {
  if (!date) return '';
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Computed property for calendar days
const calendarDays = computed(() => {
  const days = [];
  const firstDayOfMonth = new Date(currentYear.value, currentMonth.value, 1);
  const lastDayOfMonth = new Date(currentYear.value, currentMonth.value + 1, 0);
  const numDaysInMonth = lastDayOfMonth.getDate();
  const firstDayOfWeek = firstDayOfMonth.getDay(); // 0 for Sunday, 1 for Monday...

  // Add leading empty days for previous month
  for (let i = 0; i < firstDayOfWeek; i++) {
    days.push({ dayOfMonth: null, date: null, hasRecords: false, isCurrentMonth: false, status: [] });
  }

  // Add days of the current month
  const today = new Date();
  const todayYYYYMMDD = formatDateToYYYYMMDD(today);

  for (let i = 1; i <= numDaysInMonth; i++) {
    const date = new Date(currentYear.value, currentMonth.value, i);
    const dateString = formatDateToYYYYMMDD(date);
    const recordsForDay = monthlyRecords.value.filter(
      (r) => r.dateString === dateString
    );
    const hasRecords = recordsForDay.length > 0;
    const isToday = dateString === todayYYYYMMDD;

    // Collect all unique statuses for the day
    const dayStatuses = new Set();
    recordsForDay.forEach(record => {
      if (record.status && record.status !== 'normal') { // Only track 'late', 'early', 'makeup' for display on calendar
        dayStatuses.add(record.status);
      }
    });

    days.push({
      dayOfMonth: i,
      date: date,
      dateString: dateString,
      hasRecords: hasRecords,
      isToday: isToday,
      isCurrentMonth: true,
      records: recordsForDay, // Attach records for detail view
      status: Array.from(dayStatuses) // Array of statuses for the day
    });
  }

  // Add trailing empty days for next month to fill the grid (total 42 cells for 6 weeks)
  const remainingCells = 42 - days.length;
  for (let i = 0; i < remainingCells; i++) {
    days.push({ dayOfMonth: null, date: null, hasRecords: false, isCurrentMonth: false, status: [] });
  }

  return days;
});

// Computed property for selected date details
const selectedDateDetails = computed(() => {
  if (!selectedDate.value) {
    // 如果没有手动选择日期，默认显示今天的记录（如果有）
    const today = new Date();
    const todayYYYYMMDD = formatDateToYYYYMMDD(today);
    const todayRecords = monthlyRecords.value.filter(r => r.dateString === todayYYYYMMDD);
    if (todayRecords.length > 0) {
      return {
        dateString: todayYYYYMMDD,
        records: [...todayRecords].sort((a,b) => new Date(a.date) - new Date(b.date)) // 确保今日记录按时间排序
      };
    }
    return null; // 今天也没有记录
  }
  const dateString = formatDateToYYYYMMDD(selectedDate.value);
  const records = monthlyRecords.value.filter(r => r.dateString === dateString);
  return { dateString, records: [...records].sort((a,b) => new Date(a.date) - new Date(b.date)) }; // 确保选中日期记录按时间排序
});


// All clock-ins for the selected month (for the list below calendar)
const allClockIns = computed(() => {
  // Sort by date and then by time for chronological order
  return [...monthlyRecords.value].sort((a, b) => {
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    // Sort by date descending
    if (dateA.toDateString() !== dateB.toDateString()) {
      return dateB.getTime() - dateA.getTime();
    }
    // For same date, sort by time ascending
    const timeA = new Date(`2000/01/01 ${a.time}`); // Use arbitrary date for time comparison
    const timeB = new Date(`2000/01/01 ${b.time}`);
    return timeA.getTime() - timeB.getTime();
  });
});


// Calendar actions
const changeMonth = (offset) => {
  currentMonth.value += offset;
  if (currentMonth.value < 0) {
    currentMonth.value = 11;
    currentYear.value--;
  } else if (currentMonth.value > 11) {
    currentMonth.value = 0;
    currentYear.value++;
  }
  selectedDate.value = null; // Reset selected date when changing month
  attendanceStore.fetchMonthlyRecords(currentYear.value, currentMonth.value + 1);
};

const selectDay = (day) => {
  if (day.dayOfMonth !== null && day.isCurrentMonth) {
    selectedDate.value = day.date;
  }
};


onMounted(() => {
  attendanceStore.fetchAttendanceData(); // Fetch summary stats (today's status, monthly late count)
  attendanceStore.fetchMonthlyRecords(currentYear.value, currentMonth.value + 1); // Fetch initial month records for calendar/list
});

// Watch monthlyRecords to update selectedDateDetails if it's currently showing today and records change
watch(monthlyRecords, (newRecords) => {
  // If no specific date is manually selected, and today has records, select today
  if (!selectedDate.value) {
    const today = new Date();
    const todayYYYYMMDD = formatDateToYYYYMMDD(today);
    const todayRecords = newRecords.filter(r => r.dateString === todayYYYYMMDD);
    if (todayRecords.length > 0) {
      selectedDate.value = today;
    }
  } else {
    // If a date was already selected, ensure its details are refreshed based on new monthlyRecords
    const currentlySelectedDateString = formatDateToYYYYMMDD(selectedDate.value);
    const newRecordsForSelectedDate = newRecords.filter(r => r.dateString === currentlySelectedDateString);
    if (newRecordsForSelectedDate.length > 0) {
        // Force re-evaluation of selectedDateDetails computed property by changing selectedDate reference
        selectedDate.value = new Date(selectedDate.value);
    } else {
        selectedDate.value = null; // If selected date no longer has records, deselect
    }
  }
});


</script>

<style scoped lang="scss">
@use '../styles/ancient-pixel.scss'; // 引入新的 SCSS 文件

.attendance-page {
  padding: 20px;
  min-height: calc(100vh - 60px);
  font-family: 'Pixelify Sans', monospace;
  color: var(--color-ancient-dark-brown);
}

h1 {
  color: var(--color-ancient-ink);
  margin-bottom: 25px;
  text-align: left;
  font-size: 2em;
  font-family: 'ZCOOL KuaiLe', serif; // Use the themed font for h1
}

.card {
  margin-bottom: 20px;
}

.card-title {
  font-size: 1.5em;
  color: var(--color-ancient-ink);
  margin-bottom: 15px;
  text-align: center;
  border-bottom: 2px dashed var(--color-ancient-light-brown);
  padding-bottom: 10px;
  font-family: 'ZCOOL KuaiLe', serif;
}

.loading-text, .error-text, .empty-records {
  text-align: center;
  color: var(--color-ancient-light-brown);
  font-style: italic;
  padding: 10px 0;
  font-family: 'Noto Serif SC', serif;
}

.error-text {
  color: var(--color-ancient-blood-red);
  font-weight: bold;
}

/* 考勤概览统计模块 */
.attendance-summary-module {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    padding: 10px 0;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--color-ancient-paper);
    border: 2px solid var(--color-ancient-dark-brown);
    border-radius: 0;
    box-shadow: 3px 3px 0px var(--color-ancient-light-brown);
    padding: 15px;
    text-align: center;
    font-family: 'Pixelify Sans', monospace;
  }

  .stat-label {
    font-size: 0.9em;
    color: var(--color-ancient-dark-brown);
    margin-bottom: 5px;
  }

  .stat-value {
    font-size: 1.5em;
    font-weight: bold;
    color: var(--color-ancient-gold);
    text-shadow: 1px 1px 0px var(--color-ancient-dark-brown);
  }
}

/* 我的打卡日历模块 */
.attendance-calendar-card {
  .calendar-content {
    padding: 15px;
    background-color: var(--color-ancient-paper);
    border: 2px solid var(--color-ancient-dark-brown);
    box-shadow: 4px 4px 0px var(--color-ancient-light-brown);
  }

  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-family: 'Pixelify Sans', monospace;
    font-weight: bold;
    color: var(--color-ancient-ink);

    .pixel-button { // Apply general pixel-button styles
        // Directly extend the class from ancient-pixel.scss
        @extend .pixel-button;
        padding: 5px 10px;
        font-size: 0.9em;
    }
  }

  .weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    font-weight: bold;
    color: var(--color-ancient-dark-brown);
    margin-bottom: 10px;
    border-bottom: 1px dashed var(--color-ancient-light-brown);
    padding-bottom: 5px;
  }

  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
  }

  .calendar-day {
    position: relative;
    padding: 10px 5px;
    border: 1px solid var(--color-ancient-stone-gray-light);
    border-radius: 0;
    text-align: center;
    cursor: pointer;
    background-color: var(--color-ancient-paper);
    min-height: 60px; // Ensure consistent height
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    &:hover:not(.is-empty):not(.is-today) {
        background-color: var(--color-ancient-highlight);
        transform: translate(-1px, -1px);
        box-shadow: 2px 2px 0px var(--color-ancient-light-brown);
    }
    &.is-empty { // Days from prev/next month
        background-color: var(--color-ancient-stone-gray-light);
        color: var(--color-ancient-stone-gray-dark);
        opacity: 0.6;
        cursor: default;
    }
    &.is-current-month {
        color: var(--color-ancient-dark-brown);
    }
    &.is-today {
        border: 2px solid var(--color-ancient-jade);
        background-color: var(--color-ancient-jade-light);
        font-weight: bold;
        color: var(--color-ancient-ink);
        box-shadow: 2px 2px 0px var(--color-ancient-jade-dark);
    }
    &.has-records {
        font-weight: bold;
    }
    &.is-selected {
        border: 2px solid var(--color-ancient-gold);
        background-color: var(--color-ancient-gold-light);
        box-shadow: 2px 2px 0px var(--color-ancient-gold);
        color: var(--color-ancient-ink);
    }

    .day-number {
      font-size: 1.1em;
      margin-bottom: 5px;
    }

    .record-indicator {
      display: flex;
      gap: 3px;
      margin-top: auto; // Push to bottom
      span {
        font-size: 0.7em;
        font-weight: bold;
        padding: 2px 4px;
        border-radius: 0;
        border: 1px solid currentColor; // Border same color as text
        line-height: 1;
      }
      .late {
        color: var(--color-ancient-blood-red);
      }
      .early {
        color: orange; // Placeholder color for early
      }
      // Add other statuses like 'makeup' if needed
    }
  }

  .selected-date-details {
    margin-top: 20px;
    padding: 15px;
    @extend .ancient-pixel-container; // Inherit card styles
    .card-title {
        font-size: 1.2em;
        margin-bottom: 10px;
    }
    .detail-record-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 0;
        border-bottom: 1px dashed var(--color-ancient-stone-gray-light);
        &:last-child {
            border-bottom: none;
        }
        span {
            font-size: 0.9em;
        }
        .record-type {
            font-weight: bold;
        }
        .record-status {
            font-weight: bold;
        }
    }
  }
}

/* 历史打卡记录模块 */
.attendance-records-card {
  .records-list {
    padding: 10px 0;
  }

  .record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--color-ancient-paper);
    border: 1px solid var(--color-ancient-light-brown);
    border-radius: 0;
    padding: 10px 15px;
    margin-bottom: 8px;
    box-shadow: 1px 1px 0px var(--color-ancient-light-brown);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .record-date {
    font-size: 1.1em;
    font-weight: bold;
    color: var(--color-ancient-ink);
    flex-shrink: 0;
    margin-right: 15px;
  }

  .record-details {
    flex-grow: 1;
    text-align: right;
    p {
      margin: 3px 0;
      font-size: 0.9em;
      color: var(--color-ancient-dark-brown);
      font-family: 'Pixelify Sans', monospace;
    }
    .record-hours-worked {
      font-weight: bold;
      color: var(--color-ancient-jade);
    }
    .record-status {
      font-weight: bold;
    }
  }
  .empty-records {
    a {
      color: var(--color-ancient-jade);
      text-decoration: underline;
      &:hover {
        color: var(--color-ancient-jade-dark);
      }
    }
  }
}


/* 响应式调整 */
@media (max-width: 768px) {
  .attendance-page {
    padding: 15px;
  }

  h1 {
    font-size: 1.8em;
    text-align: center;
  }

  .card-title {
    font-size: 1.3em;
  }

  .attendance-summary-module .stats-grid {
    grid-template-columns: 1fr;
  }

  .attendance-summary-module .stat-item {
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px;
    .stat-value {
      font-size: 1.2em;
    }
  }

  .attendance-calendar-card .calendar-day {
      min-height: 40px;
      padding: 5px;
      .day-number { font-size: 0.9em; }
      .record-indicator {
          span { font-size: 0.6em; padding: 1px 3px; }
      }
  }

  .selected-date-details {
      .detail-record-item {
          span { font-size: 0.8em; }
      }
  }

  .record-item {
    flex-direction: column;
    align-items: flex-start;
    .record-details {
      width: 100%;
      text-align: left;
    }
  }
}

@media (max-width: 480px) {
   .attendance-page {
      padding: 10px;
   }
    h1 {
      font-size: 1.5em;
      margin-bottom: 15px;
    }

   .card-title {
      font-size: 1.1em;
   }

   .attendance-summary-module .stat-item {
      padding: 8px 12px;
      .stat-label {
        font-size: 0.8em;
      }
      .stat-value {
        font-size: 1em;
      }
   }

   .attendance-calendar-card {
       .calendar-header {
           .pixel-button { font-size: 0.8em; padding: 3px 6px; }
           .current-month-year { font-size: 0.9em; }
       }
       .weekdays span { font-size: 0.8em; }
       .calendar-day {
           min-height: 30px;
           padding: 3px;
           .day-number { font-size: 0.8em; }
           .record-indicator {
               span { font-size: 0.5em; padding: 1px 2px; }
           }
       }
   }

   .selected-date-details {
       padding: 10px;
       .card-title { font-size: 1em; }
       .detail-record-item {
           span { font-size: 0.7em; }
       }
   }

   .record-item {
      padding: 8px 10px;
      .record-date {
        font-size: 1em;
      }
      .record-details p {
        font-size: 0.8em;
      }
   }
}
</style>
```

---

**测试与验证步骤：**

1.  **更新后端依赖并重启：**
    *   进入 `backend` 目录。
    *   **重要：** 确保您已经安装了所有依赖。运行 `npm install`。
    *   运行 `npm run dev` 启动后端服务器。

2.  **更新前端依赖并重启：**
    *   进入 `frontend` 目录。
    *   运行 `npm install`。
    *   运行 `npm run dev` 启动前端应用。

3.  **登录应用：**
    *   在浏览器中访问 `http://localhost:5173` 并登录。

4.  **测试迟到/早退逻辑：**
    *   **准备：** 了解当前时间。
    *   **迟到测试：**
        *   修改 `backend/src/controllers/clockin.controller.js` 中的 `STANDARD_WORK_START` 和 `GRACE_PERIOD_MINUTES`，例如，如果现在是 10:05，您可以设置 `STANDARD_WORK_START = '10:00'`，`GRACE_PERIOD_MINUTES = 0` (或者一个很小的值，例如 1)，让当前的“入定”操作必然迟到。
        *   回到 Dashboard 页面，点击“☀️ 早课入定”。
        *   访问“修行日志”页面 (`/attendance`)。
        *   验证“修行概览”中的“本月走火入魔”计数是否增加。
        *   在“我的修行日历”中，点击今天的日期，确认详情中显示该“入定”记录的状态为 `[走火]`。日历格子上也应显示“走”的指示。
        *   在“历史修行卷宗”列表中，找到该记录，确认其状态为 `[走火]`。
    *   **早退测试：**
        *   确保您当天已打过上班卡。
        *   修改 `backend/src/controllers/clockin.controller.js` 中的 `STANDARD_WORK_END` 和 `GRACE_PERIOD_MINUTES`，例如，如果现在是 17:55，您可以设置 `STANDARD_WORK_END = '18:00'`，`GRACE_PERIOD_MINUTES = 0`，让当前的“出定”操作必然早退。
        *   回到 Dashboard 页面，点击“🌙 晚课出定”。
        *   访问“修行日志”页面 (`/attendance`)。
        *   在“我的修行日历”中，点击今天的日期，确认详情中显示该“出定”记录的状态为 `[早退]`。日历格子上应显示“早”的指示。
        *   在“历史修行卷宗”列表中，找到该记录，确认其状态为 `[早退]`。
    *   **正常打卡：** 确保在标准作息时间和宽限期内进行“入定”和“出定”，验证状态是否为 `[正常]`。

5.  **测试修行日志页面（前端）：**
    *   导航到 `/attendance` 页面。
    *   **修行概览：** 确认“今日修行时长”、“本月走火入魔”、“本月闭关天数”显示正确。
    *   **我的修行日历：**
        *   确认当前月份和年份正确显示。
        *   尝试点击“前一月”、“后一月”按钮，日历应随之切换，并加载对应月份的打卡记录。
        *   观察哪些日期被标记为“有记录”（has-records），今天是否被标记为“is-today”，手动点击的日期是否被标记为“is-selected”。
        *   点击日历中的任何一个有记录的日期，下方应显示该日期的详细打卡记录（入定、出定、修行时长、状态）。
        *   验证日历格子上是否正确显示“走”或“早”的迟到/早退指示。
    *   **历史修行卷宗：**
        *   确认列表显示了当前月份的所有打卡记录，按日期倒序排列，同一天的记录按时间升序排列。
        *   每条记录都应包含“入定/出定”、“时间”、“修行时长”（下班卡有）、“状态（正常/走火/早退）”。

完成这些测试后，我们就成功完善了打卡闭环的后端逻辑和前端“修行日志”页面的核心展示。